// Admin Dashboard JavaScript
class AdminDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.init();
    }

    init() {
        this.checkAuthentication();
        this.setupEventListeners();
    }

    async checkAuthentication() {
        const token = window.apiClient.getToken();
        const user = window.apiClient.getCurrentUser();

        if (!token || !user) {
            this.showLoginForm();
            return;
        }

        // Verify token is still valid and user is admin
        try {
            const response = await window.apiClient.request('/api/auth/me', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.success && response.data && (response.data.role === 'admin' || response.data.role === 'super_admin')) {
                this.showDashboard(response.data);
            } else {
                this.showLoginForm();
            }
        } catch (error) {
            console.error('Authentication check failed:', error);
            this.showLoginForm();
        }
    }

    showLoginForm() {
        document.getElementById('auth-check').classList.remove('hidden');
        document.getElementById('admin-dashboard').classList.add('hidden');
    }

    showDashboard(user) {
        document.getElementById('auth-check').classList.add('hidden');
        document.getElementById('admin-dashboard').classList.remove('hidden');
        
        // Update user info in sidebar
        document.getElementById('admin-name').textContent = `${user.first_name} ${user.last_name}`;
        document.getElementById('admin-role').textContent = user.role === 'super_admin' ? 'Super Administrator' : 'Administrator';
        
        // Load dashboard data
        this.loadDashboardData();
    }

    setupEventListeners() {
        // Login form
        document.getElementById('admin-login-form').addEventListener('submit', this.handleLogin.bind(this));

        // Logout button
        document.getElementById('admin-logout').addEventListener('click', this.handleLogout.bind(this));

        // Sidebar navigation
        document.querySelectorAll('.sidebar-link').forEach(link => {
            link.addEventListener('click', this.handleNavigation.bind(this));
        });

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const sidebar = document.querySelector('.admin-sidebar');

        if (mobileMenuBtn && sidebar) {
            mobileMenuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('mobile-open');
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                    sidebar.classList.remove('mobile-open');
                }
            });
        }

        // User menu toggle (if implemented)
        const userMenuBtn = document.getElementById('user-menu-btn');
        if (userMenuBtn) {
            userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                // Add user menu dropdown functionality here if needed
            });
        }
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const email = document.getElementById('admin-email').value;
        const password = document.getElementById('admin-password').value;
        
        try {
            const response = await window.apiClient.login({ email, password });
            
            if (response.success && response.data) {
                // Check if user is admin
                if (response.data.user.role === 'admin' || response.data.user.role === 'super_admin') {
                    window.apiClient.setToken(response.data.token);
                    window.apiClient.setCurrentUser(response.data.user);
                    this.showDashboard(response.data.user);
                } else {
                    this.showError('Access denied. Admin privileges required.');
                }
            } else {
                this.showError(response.message || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showError('Login failed. Please check your credentials.');
        }
    }

    async handleLogout() {
        // Show confirmation dialog
        const confirmed = confirm('Are you sure you want to logout?');
        if (!confirmed) return;

        try {
            // Show loading state
            const logoutBtn = document.getElementById('admin-logout');
            const originalText = logoutBtn.innerHTML;
            logoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Logging out...';
            logoutBtn.disabled = true;

            // Call server logout endpoint (optional - for session cleanup)
            try {
                await window.apiClient.request('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${window.apiClient.getToken()}`
                    }
                });
            } catch (error) {
                console.warn('Server logout failed, continuing with client logout:', error);
            }

            // Clear client-side data
            window.apiClient.removeToken();
            window.apiClient.removeCurrentUser();

            // Clear any other stored data
            localStorage.removeItem('cartItems');
            sessionStorage.clear();

            // Show success message
            this.showNotification('Logged out successfully', 'success');

            // Redirect to homepage after a short delay
            setTimeout(() => {
                window.location.href = '/';
            }, 1500);

        } catch (error) {
            console.error('Logout error:', error);

            // Reset button state
            const logoutBtn = document.getElementById('admin-logout');
            logoutBtn.innerHTML = '<i class="fas fa-sign-out-alt mr-2"></i>Logout';
            logoutBtn.disabled = false;

            this.showError('Logout failed. Please try again.');
        }
    }

    handleNavigation(e) {
        e.preventDefault();
        
        const section = e.currentTarget.dataset.section;
        if (section) {
            this.switchSection(section);
        }
    }

    switchSection(section) {
        // Update active sidebar link
        document.querySelectorAll('.sidebar-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');
        
        // Hide all sections
        document.querySelectorAll('.section').forEach(sec => {
            sec.classList.add('hidden');
        });
        
        // Show selected section
        document.getElementById(`${section}-section`).classList.remove('hidden');
        
        // Update page title
        const titles = {
            dashboard: { title: 'Dashboard', subtitle: 'Welcome to your admin dashboard' },
            products: { title: 'Products', subtitle: 'Manage your product catalog' },
            orders: { title: 'Orders', subtitle: 'View and manage customer orders' },
            users: { title: 'Users', subtitle: 'Manage user accounts and permissions' },
            analytics: { title: 'Analytics', subtitle: 'View detailed analytics and reports' },
            settings: { title: 'Settings', subtitle: 'Configure your store settings' }
        };
        
        const pageInfo = titles[section] || { title: 'Dashboard', subtitle: 'Welcome to your admin dashboard' };
        document.getElementById('page-title').textContent = pageInfo.title;
        document.getElementById('page-subtitle').textContent = pageInfo.subtitle;
        
        this.currentSection = section;
        
        // Load section-specific data
        this.loadSectionData(section);
    }

    async loadSectionData(section) {
        switch (section) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'products':
                await this.loadProductsData();
                break;
            case 'orders':
                await this.loadOrdersData();
                break;
            case 'users':
                await this.loadUsersData();
                break;
            case 'analytics':
                await this.loadAnalyticsData();
                break;
            case 'settings':
                await this.loadSettingsData();
                break;
        }
    }

    async loadDashboardData() {
        try {
            const response = await window.apiClient.getDashboardAnalytics();
            
            if (response.success && response.data) {
                this.updateDashboardStats(response.data.totals);
                this.updateRecentOrders(response.data.recentOrders);
                this.updatePopularProducts(response.data.popularProducts);
            }
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.showError('Failed to load dashboard data');
        }
    }

    updateDashboardStats(totals) {
        document.getElementById('total-users').textContent = totals.users.toLocaleString();
        document.getElementById('total-products').textContent = totals.products.toLocaleString();
        document.getElementById('total-orders').textContent = totals.orders.toLocaleString();
        document.getElementById('total-revenue').textContent = `$${totals.revenue.toLocaleString()}`;
    }

    updateRecentOrders(orders) {
        const container = document.getElementById('recent-orders');
        
        if (!orders || orders.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">No recent orders</p>';
            return;
        }

        container.innerHTML = orders.map(order => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-800">#${order.order_number}</p>
                    <p class="text-sm text-gray-600">${order.customer_name}</p>
                </div>
                <div class="text-right">
                    <p class="font-medium text-gray-800">$${order.total_amount}</p>
                    <span class="text-xs px-2 py-1 rounded-full ${this.getStatusColor(order.status)}">${order.status}</span>
                </div>
            </div>
        `).join('');
    }

    updatePopularProducts(products) {
        const container = document.getElementById('popular-products');
        
        if (!products || products.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">No product data available</p>';
            return;
        }

        container.innerHTML = products.map(product => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-800">${product.name}</p>
                    <p class="text-sm text-gray-600">Stock: ${product.stock_quantity}</p>
                </div>
                <div class="text-right">
                    <p class="font-medium text-gray-800">${product.total_sold || 0} sold</p>
                    <p class="text-sm text-gray-600">$${product.price}</p>
                </div>
            </div>
        `).join('');
    }



    getStatusColor(status) {
        const colors = {
            pending: 'bg-yellow-100 text-yellow-800',
            processing: 'bg-blue-100 text-blue-800',
            shipped: 'bg-purple-100 text-purple-800',
            delivered: 'bg-green-100 text-green-800',
            cancelled: 'bg-red-100 text-red-800',
            refunded: 'bg-gray-100 text-gray-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    }

    async loadProductsData() {
        // Placeholder for products management
        console.log('Loading products data...');
    }

    async loadOrdersData() {
        // Placeholder for orders management
        console.log('Loading orders data...');
    }

    async loadUsersData() {
        // Placeholder for users management
        console.log('Loading users data...');
    }

    async loadAnalyticsData() {
        // Placeholder for analytics
        console.log('Loading analytics data...');
    }

    async loadSettingsData() {
        // Placeholder for settings
        console.log('Loading settings data...');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

        // Set notification style based on type
        switch (type) {
            case 'success':
                notification.className += ' bg-green-500 text-white';
                break;
            case 'error':
                notification.className += ' bg-red-500 text-white';
                break;
            case 'warning':
                notification.className += ' bg-yellow-500 text-white';
                break;
            default:
                notification.className += ' bg-blue-500 text-white';
        }

        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize admin dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdminDashboard();
});
