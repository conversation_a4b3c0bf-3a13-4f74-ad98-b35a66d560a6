<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Luxe Fashion</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="Luxe Fashion Admin Dashboard - Manage your e-commerce store">
    <meta name="keywords" content="admin, dashboard, e-commerce, fashion, management">
    <meta name="author" content="Luxe Fashion">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="Admin Dashboard - Luxe Fashion">
    <meta property="og:description" content="Luxe Fashion Admin Dashboard - Manage your e-commerce store">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://luxefashion.com/admin">
    
    <!-- Twitter Card tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Admin Dashboard - Luxe Fashion">
    <meta name="twitter:description" content="Luxe Fashion Admin Dashboard - Manage your e-commerce store">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- External CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
    <script src="api-config.js"></script>
    
    <style>
        .admin-sidebar {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
        }

        .admin-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
        }

        .stat-card.users {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        }

        .stat-card.users:hover {
            box-shadow: 0 20px 40px rgba(6, 182, 212, 0.3);
        }

        .stat-card.products {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .stat-card.products:hover {
            box-shadow: 0 20px 40px rgba(16, 185, 129, 0.3);
        }

        .stat-card.orders {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .stat-card.orders:hover {
            box-shadow: 0 20px 40px rgba(245, 158, 11, 0.3);
        }

        .stat-card.revenue {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .stat-card.revenue:hover {
            box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
        }

        .table-container {
            max-height: 600px;
            overflow-y: auto;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }

        .sidebar-link {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 8px;
            margin: 2px 8px;
        }

        .sidebar-link:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(8px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .sidebar-link.active {
            background: rgba(255, 255, 255, 0.25);
            border-left: 4px solid #fbbf24;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .admin-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
            backdrop-filter: blur(10px);
        }

        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        }

        .form-input {
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
        }

        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 50;
                transition: transform 0.3s ease;
            }

            .admin-sidebar.mobile-open {
                transform: translateX(0);
            }

            .admin-content {
                margin-left: 0 !important;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-inter">
    <!-- Admin Authentication Check -->
    <div id="auth-check" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <i class="fas fa-shield-alt text-4xl text-primary mb-4"></i>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Admin Access Required</h2>
                <p class="text-gray-600 mb-6">Please log in with your admin credentials to access the dashboard.</p>
                
                <form id="admin-login-form" class="space-y-6">
                    <div>
                        <label for="admin-email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <input type="email" id="admin-email" placeholder="Enter your admin email" required
                               class="form-input w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="admin-password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <input type="password" id="admin-password" placeholder="Enter your password" required
                               class="form-input w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <button type="submit" class="w-full btn-primary text-white py-3 rounded-lg font-medium">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Login to Dashboard
                    </button>
                </form>
                
                <div class="mt-4 text-sm text-gray-500">
                    <p>Demo Credentials:</p>
                    <p>Email: <EMAIL></p>
                    <p>Password: admin123</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Admin Dashboard -->
    <div id="admin-dashboard" class="hidden min-h-screen flex">
        <!-- Sidebar -->
        <div class="admin-sidebar w-64 min-h-screen text-white">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-8">
                    <i class="fas fa-crown text-2xl text-yellow-300"></i>
                    <div>
                        <h1 class="text-xl font-bold">Luxe Fashion</h1>
                        <p class="text-sm opacity-75">Admin Dashboard</p>
                    </div>
                </div>
                
                <nav class="space-y-2">
                    <a href="#" class="sidebar-link active flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="dashboard">
                        <i class="fas fa-chart-line"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="#" class="sidebar-link flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="products">
                        <i class="fas fa-box"></i>
                        <span>Products</span>
                    </a>
                    <a href="#" class="sidebar-link flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="orders">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Orders</span>
                    </a>
                    <a href="#" class="sidebar-link flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="users">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </a>
                    <a href="#" class="sidebar-link flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                    <a href="#" class="sidebar-link flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </nav>
            </div>
            
            <div class="absolute bottom-0 w-64 p-6 border-t border-white border-opacity-20">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-sm"></i>
                    </div>
                    <div>
                        <p class="font-medium" id="admin-name">Admin User</p>
                        <p class="text-sm opacity-75" id="admin-role">Administrator</p>
                    </div>
                </div>
                <button id="admin-logout" class="w-full btn-danger text-white py-3 rounded-lg font-medium transition-all duration-300 hover:shadow-lg">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    Logout
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-hidden">
            <!-- Mobile Menu Button -->
            <button id="mobile-menu-btn" class="md:hidden fixed top-4 left-4 z-50 bg-white p-2 rounded-lg shadow-lg">
                <i class="fas fa-bars text-gray-700"></i>
            </button>

            <!-- Header -->
            <header class="admin-header px-6 py-4 sticky top-0 z-40">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div>
                            <h2 id="page-title" class="text-2xl font-bold text-gray-800">Dashboard</h2>
                            <p id="page-subtitle" class="text-gray-600 text-sm">Welcome to your admin dashboard</p>
                        </div>
                        <div class="hidden md:flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-full">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span class="text-sm text-blue-700 font-medium">Live</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <!-- Notifications -->
                        <button class="relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>

                        <!-- Quick Actions -->
                        <div class="hidden md:flex items-center space-x-2">
                            <button class="btn-primary text-white px-4 py-2 rounded-lg text-sm font-medium">
                                <i class="fas fa-plus mr-2"></i>
                                Add Product
                            </button>
                            <a href="/" target="_blank" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all duration-200 text-sm font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                View Site
                            </a>
                        </div>

                        <!-- User Menu -->
                        <div class="relative">
                            <button id="user-menu-btn" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-all duration-200">
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                                <i class="fas fa-chevron-down text-gray-500 text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <main class="p-6 overflow-y-auto" style="height: calc(100vh - 80px);">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="section">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="stat-card users rounded-xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Users</p>
                                    <p id="total-users" class="text-3xl font-bold">0</p>
                                </div>
                                <i class="fas fa-users text-3xl text-white text-opacity-60"></i>
                            </div>
                        </div>
                        
                        <div class="stat-card products rounded-xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Products</p>
                                    <p id="total-products" class="text-3xl font-bold">0</p>
                                </div>
                                <i class="fas fa-box text-3xl text-white text-opacity-60"></i>
                            </div>
                        </div>
                        
                        <div class="stat-card orders rounded-xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Orders</p>
                                    <p id="total-orders" class="text-3xl font-bold">0</p>
                                </div>
                                <i class="fas fa-shopping-cart text-3xl text-white text-opacity-60"></i>
                            </div>
                        </div>
                        
                        <div class="stat-card revenue rounded-xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Revenue</p>
                                    <p id="total-revenue" class="text-3xl font-bold">$0</p>
                                </div>
                                <i class="fas fa-dollar-sign text-3xl text-white text-opacity-60"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <div class="admin-card rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Monthly Sales</h3>
                            <canvas id="salesChart" width="400" height="200"></canvas>
                        </div>
                        
                        <div class="admin-card rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">User Registrations</h3>
                            <canvas id="usersChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="admin-card rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Orders</h3>
                            <div id="recent-orders" class="space-y-3">
                                <!-- Recent orders will be loaded here -->
                            </div>
                        </div>
                        
                        <div class="admin-card rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Popular Products</h3>
                            <div id="popular-products" class="space-y-3">
                                <!-- Popular products will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other sections will be added here -->
                <div id="products-section" class="section hidden">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Product Management</h3>
                        <p class="text-gray-600">Product management features coming soon...</p>
                    </div>
                </div>

                <div id="orders-section" class="section hidden">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Order Management</h3>
                        <p class="text-gray-600">Order management features coming soon...</p>
                    </div>
                </div>

                <div id="users-section" class="section hidden">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">User Management</h3>
                        <p class="text-gray-600">User management features coming soon...</p>
                    </div>
                </div>

                <div id="analytics-section" class="section hidden">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Analytics</h3>
                        <p class="text-gray-600">Advanced analytics coming soon...</p>
                    </div>
                </div>

                <div id="settings-section" class="section hidden">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Settings</h3>
                        <p class="text-gray-600">Settings panel coming soon...</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="admin.js"></script>
</body>
</html>
